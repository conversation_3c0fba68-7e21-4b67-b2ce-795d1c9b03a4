#!/usr/bin/env python3
"""
测试打开下载目录功能
"""

from pages.apps.ella.ella_multimodal_handler import EllaMultimodalHandler
from pages.apps.ella.main_page_refactored import EllaDialoguePage
from core.base_driver import driver_manager
from core.logger import log


def test_open_downloads_directory():
    """测试打开下载目录功能（优化后的精确定位逻辑）"""
    try:
        # 初始化页面和处理器
        page = EllaDialoguePage(driver_manager.driver)
        handler = EllaMultimodalHandler(driver_manager.driver, page.page_elements)

        log.info("开始测试打开下载目录功能（使用精确定位）...")
        log.info("预期行为：")
        log.info("1. 点击菜单按钮（className=android.widget.ImageButton）")
        log.info("2. 精确定位并点击Downloads选项（id=android:id/title 且 text=Downloads）")

        # 测试打开下载目录
        result = handler._open_downloads_directory()

        if result:
            log.info("✅ 打开下载目录测试成功")
        else:
            log.warning("⚠️ 打开下载目录测试失败")

        return result

    except Exception as e:
        log.error(f"测试打开下载目录功能失败: {e}")
        return False


def test_select_specific_file_with_downloads():
    """测试带下载目录打开的文件选择功能"""
    try:
        # 初始化页面和处理器
        page = EllaDialoguePage(driver_manager.driver)
        handler = EllaMultimodalHandler(driver_manager.driver, page.page_elements)
        
        log.info("开始测试带下载目录打开的文件选择功能...")
        
        # 测试选择特定文件（会自动先打开下载目录）
        result = handler._select_specific_file("bcy_doc.txt")
        
        if result:
            log.info("✅ 文件选择测试成功")
        else:
            log.warning("⚠️ 文件选择测试失败")
            
        return result
        
    except Exception as e:
        log.error(f"测试文件选择功能失败: {e}")
        return False


if __name__ == "__main__":
    # 测试1: 单独测试打开下载目录
    log.info("=" * 50)
    log.info("测试1: 打开下载目录")
    test_open_downloads_directory()
    
    # 测试2: 测试完整的文件选择流程
    log.info("=" * 50)
    log.info("测试2: 文件选择（包含打开下载目录）")
    test_select_specific_file_with_downloads()
