"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenSetAnAlarmAmTomorrow(SimpleEllaTest):
    """Ella打开Set an alarm at 10 am tomorrow测试类"""

    @allure.title("测试Set an alarm at 10 am tomorrow")
    @allure.description("测试Set an alarm at 10 am tomorrow指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_set_an_alarm_at_am_tomorrow(self, ella_app):
        """测试Set an alarm at 10 am tomorrow命令"""
        command = "Set an alarm at 10 am tomorrow"
        expected_text = ['Done','10 AM']

        tmp = []
        with allure.step(f"执行命令: delete all the alarms"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, 'delete all the alarms', verify_status=True, verify_files=False
            )
            tmp.append(response_text)

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=False, verify_files=False
            )
            tmp.append(response_text)
        with allure.step(f"执行命令: delete all the alarms"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, 'get all the alarms', verify_status=True, verify_files=False
            )
            tmp.append(response_text)

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, tmp)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
