"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("打开应用")
class TestEllaOpenSetAnAlarmAmTomorrow(SimpleEllaTest):
    """Ella打开Set an alarm at 10 am tomorrow测试类"""

    @allure.title("测试Set an alarm at 10 am tomorrow")
    @allure.description("测试Set an alarm at 10 am tomorrow指令")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_set_an_alarm_at_am_tomorrow(self, ella_app):
        """测试Set an alarm at 10 am tomorrow命令"""
        command = "Set an alarm at 10 am tomorrow"
        expected_text = ['Tomorrow','10:00']

        tmp = []
        commands = ['delete all the alarms','Set an alarm at 10 am tomorrow','get all the alarms']
        with allure.step(f"执行命令:  {commands[0]}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app,  commands[0], verify_status=True, verify_files=False
            )
            [tmp.append(x) for x in response_text if x not in commands]

        with allure.step(f"执行命令: {commands[1]}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, commands[1], verify_status=False, verify_files=False
            )
            # [tmp.append(x) for x in response_text if x not in commands]
            # 执行设置这行文案不获取，而是通过查询记录来验证

        with allure.step(f"执行命令: {commands[2]}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, commands[2], verify_status=False, verify_files=False
            )
            [tmp.append(x) for x in response_text if x not in commands]


        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, tmp)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{tmp}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")
